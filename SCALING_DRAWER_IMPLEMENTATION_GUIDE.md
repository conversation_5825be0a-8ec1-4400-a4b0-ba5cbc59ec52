# Custom Scaling Drawer Implementation Guide

## Overview
This guide explains how to implement the custom scaling drawer with shadow effects in any React Native project. This drawer creates a unique effect where the main screen scales down and slides to reveal the drawer underneath.

## Why This Implementation?
- **Visual Appeal**: Scaling effect with shadows creates depth
- **Performance**: Optimized to prevent unnecessary re-renders
- **Customizable**: Full control over animations and styling
- **No Dependencies**: Uses only React Native's built-in Animated API

## Step-by-Step Implementation

### 1. Project Setup

```bash
# Create new React Native project
npx react-native@latest init YourProjectName
cd YourProjectName

# Install required dependencies
npm install react-native-gesture-handler react-native-reanimated react-native-safe-area-context

# For iOS, install pods
cd ios && pod install && cd ..
```

### 2. File Structure
Create this folder structure in your project:

```
src/
├── hooks/
│   └── useDrawer.ts
├── components/
│   └── ScreenRenderer.tsx
├── screens/
│   ├── HomeScreen.tsx
│   ├── ProfileScreen.tsx
│   └── [YourOtherScreens].tsx
└── MainApp.tsx
```

### 3. Core Files to Copy

#### A. useDrawer.ts Hook
Copy the entire `src/hooks/useDrawer.ts` file - this handles all animations.

#### B. ScreenRenderer.tsx Component
Copy `src/components/ScreenRenderer.tsx` - this prevents unnecessary re-renders.

#### C. MainApp.tsx
Copy and modify `src/MainApp.tsx` for your screens.

### 4. Customization for Your Project

#### Update Screen Types
In MainApp.tsx, change the screen types:

```tsx
// Change this line:
type Screen = 'Home' | 'Profile' | 'Heavy';

// To your screens:
type Screen = 'Dashboard' | 'Settings' | 'Profile' | 'About';
```

#### Update Navigation Menu
Modify the drawer menu items:

```tsx
const renderDrawer = () => (
  <View style={styles.drawerBackground}>
    <SafeAreaView style={styles.drawerContent}>
      {/* Your menu items */}
      <TouchableOpacity onPress={() => navigateToScreen('Dashboard')}>
        <Text>Dashboard</Text>
      </TouchableOpacity>
      {/* Add more menu items */}
    </SafeAreaView>
  </View>
);
```

#### Update ScreenRenderer
Modify the screens object in ScreenRenderer.tsx:

```tsx
const screens = useMemo(() => ({
  Dashboard: <DashboardScreen />,
  Settings: <SettingsScreen />,
  Profile: <ProfileScreen />,
  About: <AboutScreen />,
}), []);
```

### 5. App.tsx Integration

Update your App.tsx:

```tsx
import React from 'react';
import { StatusBar } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import MainApp from './src/MainApp';

function App(): React.JSX.Element {
  return (
    <SafeAreaProvider>
      <StatusBar barStyle="dark-content" />
      <MainApp />
    </SafeAreaProvider>
  );
}

export default App;
```

### 6. Styling Customization

#### Colors
Change the drawer background color in MainApp.tsx:

```tsx
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#YOUR_COLOR', // Change drawer background
  },
  drawerBackground: {
    backgroundColor: '#YOUR_COLOR', // Match container color
  },
});
```

#### Animation Values
Modify animation parameters in useDrawer.ts:

```tsx
// Slide distance (70% of screen width)
toValue: screenWidth * 0.7,

// Scale factor (80% of original size)
toValue: 0.8,

// Animation duration
duration: 250,
```

### 7. Performance Optimizations

The implementation includes several performance optimizations:

1. **Memoized Screen Rendering**: Prevents unnecessary re-renders
2. **Native Driver**: Uses native animations for better performance
3. **Layered Architecture**: Separates concerns for better maintainability

### 8. Advanced Customizations

#### Custom Shadow Effects
Modify shadow layers in MainApp.tsx:

```tsx
// Add more shadow layers for deeper effect
<View style={styles.shadowLayer4} />
<View style={styles.shadowLayer5} />
```

#### Different Animation Curves
Use different easing functions:

```tsx
import { Easing } from 'react-native';

Animated.timing(slideAnim, {
  toValue: screenWidth * 0.7,
  duration: 300,
  easing: Easing.bezier(0.25, 0.46, 0.45, 0.94),
  useNativeDriver: true,
})
```

### 9. Testing

Test the implementation:

1. **Drawer Opening**: Tap burger menu
2. **Screen Scaling**: Verify smooth scaling animation
3. **Shadow Effects**: Check shadow visibility
4. **Performance**: Test with heavy screens
5. **Navigation**: Ensure screen switching works

### 10. Troubleshooting

Common issues and solutions:

- **Animations not smooth**: Ensure `useNativeDriver: true`
- **Shadows not visible**: Check shadow styles and elevation
- **Performance issues**: Verify ScreenRenderer memoization
- **Gesture conflicts**: Ensure proper touch handling

## Benefits of This Approach

1. **Full Control**: Complete customization over animations
2. **Performance**: Optimized for smooth animations
3. **Visual Appeal**: Unique scaling effect with shadows
4. **Reusable**: Easy to implement in multiple projects
5. **No External Dependencies**: Uses only React Native APIs

## Next Steps

After implementation:
1. Test on both iOS and Android
2. Customize colors and animations to match your brand
3. Add more screens as needed
4. Consider adding gesture support for swipe-to-open
5. Implement state persistence if needed
